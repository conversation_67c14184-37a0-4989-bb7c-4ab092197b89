<script setup>
import { h, onMounted, onUnmounted, ref, resolveDirective, withDirectives } from 'vue'
import { NButton, NTag, NForm, NFormItem, NInput, NSelect, NPopconfirm, NTooltip, useMessage, NInputNumber } from 'naive-ui'
import { useRouter } from 'vue-router'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'

import { formatDate, renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import api from '@/api'

defineOptions({ name: '页面管理' })

// Setup message service and router
const message = useMessage()
const router = useRouter()

// Table reference and query state
const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')

// Task polling state
const pollingIntervals = ref(new Map())

// Processing status configuration
const processingStatusMap = {
  NEW: {
    type: 'primary',
    text: '未处理',
    icon: 'mdi:file-document-outline',
    actionText: '开始处理',
    actionIcon: 'mdi:play',
    canAction: true
  },
  PENDING: {
    type: 'warning',
    text: '待处理',
    icon: 'mdi:clock-outline',
    actionText: '开始处理',
    actionIcon: 'mdi:play',
    canAction: true
  },
  PROCESSING: {
    type: 'info',
    text: '处理中',
    icon: 'mdi:progress-clock',
    actionText: '取消处理',
    actionIcon: 'mdi:cancel',
    canAction: true
  },
  COMPLETED: {
    type: 'success',
    text: '已完成',
    icon: 'mdi:check-circle-outline',
    actionText: '重新处理',
    actionIcon: 'mdi:restart',
    canAction: true
  },
  CANCELLED: {
    type: 'warning',
    text: '已取消',
    icon: 'mdi:cancel',
    actionText: '重新处理',
    actionIcon: 'mdi:restart',
    canAction: true
  },
  FAILED: {
    type: 'error',
    text: '失败',
    icon: 'mdi:alert-circle-outline',
    actionText: '重试',
    actionIcon: 'mdi:refresh',
    canAction: true
  }
}

// Form initialization
const initForm = {
  bundle_id: undefined,
  file_id: undefined,
  page_number: 1,
  process_status: 'NEW',
  image_url: '',
  image_width: undefined,
  image_height: undefined,
  image_ppi: undefined
}

// CRUD operations
const {
  modalVisible,
  modalTitle,
  modalLoading,
  handleAdd,
  handleDelete,
  handleEdit,
  handleSave,
  modalForm,
  modalFormRef,
} = useCRUD({
  name: '页面',
  initForm,
  doCreate: api.createPage,
  doUpdate: api.updatePage,
  doDelete: api.deletePage,
  refresh: () => $table.value?.handleSearch(),
})

// Handle page status action
async function handleStatusAction(page) {
  const status = processingStatusMap[page.process_status]
  if (!status) {
    message.error(`未知状态: ${page.process_status}`)
    return
  }

  try {
    switch (page.process_status) {
      case 'NEW':
      case 'PENDING':
      case 'CANCELLED':
      case 'FAILED':
        // Start page processing
        message.loading(`正在启动页面处理: ${page.id}`, { duration: 0 })
        const startResult = await api.processPage(page.id)
        message.destroyAll()

        if (startResult.code === 200) {
          message.success(`页面处理任务已启动: ${page.id}`)
          // Start polling for status updates
          startTaskStatusPolling(page.id, startResult.data.task_id)
        } else {
          message.error(`启动页面处理失败: ${startResult.msg || '未知错误'}`)
        }
        break

      case 'PROCESSING':
        // Stop page processing
        if (!page.process_task_id) {
          message.error('无法取消处理：缺少任务ID')
          return
        }

        message.loading(`正在取消页面处理: ${page.id}`, { duration: 0 })
        const stopResult = await api.stopTask(page.process_task_id)
        message.destroyAll()

        if (stopResult.code === 200) {
          message.success(`页面处理已取消: ${page.id}`)
          // Refresh table to update status
          $table.value?.handleSearch()
        } else {
          message.error(`取消页面处理失败: ${stopResult.msg || '未知错误'}`)
        }
        break

      case 'COMPLETED':
        // Restart page processing
        message.loading(`正在启动页面重新处理: ${page.id}`, { duration: 0 })
        const restartResult = await api.processPage(page.id)
        message.destroyAll()

        if (restartResult.code === 200) {
          message.success(`页面重新处理任务已启动: ${page.id}`)
          // Start polling for status updates
          startTaskStatusPolling(page.id, restartResult.data.task_id)
        } else {
          message.error(`启动页面重新处理失败: ${restartResult.msg || '未知错误'}`)
        }
        break

      default:
        message.info(`处理页面: ${page.id}, 状态: ${page.process_status}`)
    }
  } catch (error) {
    message.destroyAll()
    console.error('Error handling status action:', error)
    message.error(`操作失败: ${error.message || '未知错误'}`)
  }
}

// Task status polling functions
function startTaskStatusPolling(pageId, taskId) {
  // Clear any existing polling for this page
  stopTaskStatusPolling(pageId)

  // Start polling every 2 seconds
  const intervalId = setInterval(async () => {
    try {
      const statusResult = await api.getTaskStatus(taskId)
      if (statusResult.code === 200) {
        const taskStatus = statusResult.data.status

        // Check if task is finished (success, failure, or cancelled)
        if (['SUCCESS', 'FAILURE', 'REVOKED'].includes(taskStatus)) {
          // Stop polling
          stopTaskStatusPolling(pageId)

          // Refresh table to show updated status
          $table.value?.handleSearch()

          // Show completion message
          if (taskStatus === 'SUCCESS') {
            message.success(`页面处理完成`)
          } else if (taskStatus === 'FAILURE') {
            message.error(`页面处理失败`)
          } else if (taskStatus === 'REVOKED') {
            message.warning(`页面处理已取消`)
          }
        }
      }
    } catch (error) {
      console.error('Error polling task status:', error)
      // Continue polling even if there's an error
    }
  }, 2000)

  // Store the interval ID
  pollingIntervals.value.set(pageId, intervalId)
}

function stopTaskStatusPolling(pageId) {
  const intervalId = pollingIntervals.value.get(pageId)
  if (intervalId) {
    clearInterval(intervalId)
    pollingIntervals.value.delete(pageId)
  }
}

// Cleanup polling intervals when component is unmounted
function cleanupPolling() {
  pollingIntervals.value.forEach((intervalId) => {
    clearInterval(intervalId)
  })
  pollingIntervals.value.clear()
}

// Navigate to bundle detail
function navigateToBundle(bundleId) {
  router.push(`/bundle/${bundleId}`)
}

// Navigate to file detail
function navigateToFile(fileId) {
  router.push(`/file/${fileId}`)
}

// Render functions for table columns
function renderStatus(row) {
  const status = processingStatusMap[row.process_status] || {
    type: 'default',
    text: row.process_status,
    icon: 'mdi:help-circle-outline'
  }

  // Only show status icon with tooltip
  return h(
    NTooltip,
    { trigger: 'hover' },
    {
      trigger: () => h(
        NTag,
        { type: status.type, style: 'display: flex; align-items: center; justify-content: center; width: 24px; height: 24px; padding: 0;' },
        {
          default: () => h(TheIcon, { icon: status.icon, size: 16 })
        }
      ),
      default: () => status.text
    }
  )
}

function renderBundleId(row) {
  return h(
    NButton,
    {
      text: true,
      type: 'primary',
      onClick: () => navigateToBundle(row.bundle_id)
    },
    { default: () => row.bundle_id }
  )
}

function renderFileId(row) {
  if (!row.file_id) return '无'

  return h(
    NButton,
    {
      text: true,
      type: 'primary',
      onClick: () => navigateToFile(row.file_id)
    },
    { default: () => row.file_id }
  )
}

function renderActions(row) {
  const status = processingStatusMap[row.process_status]

  return h('div', { class: 'flex gap-2' }, [
    // Status action button (if available)
    status && status.canAction ? h(
      NButton,
      {
        size: 'tiny',
        quaternary: true,
        type: status.type,
        onClick: () => handleStatusAction(row),
      },
      {
        default: () => status.actionText,
        icon: renderIcon(status.actionIcon, { size: 16 })
      }
    ) : null,

    // Edit button
    h(
      NButton,
      {
        size: 'tiny',
        quaternary: true,
        type: 'info',
        onClick: () => handleEdit(row),
      },
      {
        default: () => '修改',
        icon: renderIcon('material-symbols:edit-outline', { size: 16 })
      }
    ),

    // Delete button
    h(
      NPopconfirm,
      {
        onPositiveClick: () => handleDelete(row.id),
      },
      {
        default: () => '确认删除？',
        trigger: () =>
          withDirectives(
            h(
              NButton,
              {
                size: 'tiny',
                quaternary: true,
                type: 'error',
              },
              {
                default: () => '删除',
                icon: renderIcon('material-symbols:delete-outline', { size: 16 })
              }
            ),
            [[vPermission, 'delete/api/v1/page/{page_id}']]
          ),
      }
    ),
  ])
}

// Table columns definition
const columns = [
  {
    title: 'ID',
    key: 'id',
    width: 80,
  },
  {
    title: 'Bundle ID',
    key: 'bundle_id',
    width: 100,
    render: renderBundleId
  },
  {
    title: '文件 ID',
    key: 'file_id',
    width: 100,
    render: renderFileId
  },
  {
    title: '页码',
    key: 'page_number',
    width: 80,
  },
  {
    title: '处理状态',
    key: 'process_status',
    width: 100,
    render: renderStatus
  },
  {
    title: '图像宽度',
    key: 'image_width',
    width: 100,
  },
  {
    title: '图像高度',
    key: 'image_height',
    width: 100,
  },
  {
    title: '分辨率',
    key: 'image_ppi',
    width: 100,
  },
  {
    title: '修改时间',
    key: 'updated_at',
    width: 180,
    render: (row) => formatDate(row.updated_at)
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: renderActions
  }
]

// Form validation rules
const formRules = {
  bundle_id: {
    required: true,
    type: 'number',
    message: '请输入Bundle ID',
    trigger: ['input', 'blur']
  },
  page_number: {
    required: true,
    type: 'number',
    message: '请输入页码',
    trigger: ['input', 'blur']
  }
}

// Initialize component
onMounted(() => {
  $table.value?.handleSearch()
})

// Cleanup when component is unmounted
onUnmounted(() => {
  cleanupPolling()
})
</script>

<template>
  <!-- Main page -->
  <CommonPage show-footer title="页面管理">
    <!-- Action buttons -->
    <template #action>
      <NButton
        v-permission="'post/api/v1/page/create'"
        type="primary"
        @click="handleAdd"
      >
        <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新建页面
      </NButton>
    </template>

    <!-- Page table -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="api.getPages"
    >
      <template #queryBar>
        <QueryBarItem label="Bundle ID" :label-width="80">
          <NInput
            v-model:value="queryItems.bundle_id"
            clearable
            type="text"
            placeholder="请输入Bundle ID"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>

        <QueryBarItem label="文件 ID" :label-width="80">
          <NInput
            v-model:value="queryItems.file_id"
            clearable
            type="text"
            placeholder="请输入文件 ID"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>

        <QueryBarItem label="处理状态" :label-width="80">
          <NSelect
            v-model:value="queryItems.process_status"
            clearable
            placeholder="请选择处理状态"
            :options="Object.entries(processingStatusMap).map(([value, item]) => ({
              label: item.text,
              value
            }))"
            @update:value="$table?.handleSearch()"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <!-- Create/Edit modal -->
    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      @save="handleSave"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="modalForm"
        :rules="formRules"
      >
        <NFormItem
          label="Bundle ID"
          path="bundle_id"
        >
          <NInputNumber v-model:value="modalForm.bundle_id" placeholder="请输入Bundle ID" />
        </NFormItem>

        <NFormItem
          label="文件 ID"
          path="file_id"
        >
          <NInputNumber v-model:value="modalForm.file_id" placeholder="请输入文件 ID" />
        </NFormItem>

        <NFormItem
          label="页码"
          path="page_number"
        >
          <NInputNumber v-model:value="modalForm.page_number" placeholder="请输入页码" />
        </NFormItem>

        <NFormItem
          label="处理状态"
          path="process_status"
        >
          <NSelect
            v-model:value="modalForm.process_status"
            placeholder="请选择处理状态"
            :options="Object.entries(processingStatusMap).map(([value, item]) => ({
              label: item.text,
              value
            }))"
          />
        </NFormItem>

        <NFormItem
          label="图像URL"
          path="image_url"
        >
          <NInput v-model:value="modalForm.image_url" placeholder="请输入图像URL" />
        </NFormItem>

        <NFormItem
          label="图像宽度"
          path="image_width"
        >
          <NInputNumber v-model:value="modalForm.image_width" placeholder="请输入图像宽度" />
        </NFormItem>

        <NFormItem
          label="图像高度"
          path="image_height"
        >
          <NInputNumber v-model:value="modalForm.image_height" placeholder="请输入图像高度" />
        </NFormItem>

        <NFormItem
          label="分辨率"
          path="image_ppi"
        >
          <NInputNumber v-model:value="modalForm.image_ppi" placeholder="请输入分辨率" />
        </NFormItem>
      </NForm>
    </CrudModal>
  </CommonPage>
</template>
