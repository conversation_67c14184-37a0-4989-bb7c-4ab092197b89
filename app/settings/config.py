import os
import typing

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    VERSION: str = "0.1.0"
    APP_TITLE: str = "Vue FastAPI Admin"
    PROJECT_NAME: str = "Vue FastAPI Admin"
    APP_DESCRIPTION: str = "Description"

    CORS_ORIGINS: typing.List = ["*"]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: typing.List = ["*"]
    CORS_ALLOW_HEADERS: typing.List = ["*"]

    DEBUG: bool = True

    PROJECT_ROOT: str = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
    BASE_DIR: str = os.path.abspath(os.path.join(PROJECT_ROOT, os.pardir))
    LOGS_ROOT: str = os.path.join(BASE_DIR, "app/logs")
    SECRET_KEY: str = "3488a63e1765035d386f05409663f55c83bfae3b3c61a932744b20ad14244dcf"  # openssl rand -hex 32
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7 day
    TORTOISE_ORM: dict = {
        "connections": {
            # SQLite configuration
            "sqlite": {
                "engine": "tortoise.backends.sqlite",
                "credentials": {"file_path": f"{BASE_DIR}/data/sqlite/db.sqlite3"},  # Path to SQLite database file
            },
            # MySQL/MariaDB configuration
            # Install with: tortoise-orm[asyncmy]
            # "mysql": {
            #     "engine": "tortoise.backends.mysql",
            #     "credentials": {
            #         "host": "localhost",  # Database host address
            #         "port": 3306,  # Database port
            #         "user": "yourusername",  # Database username
            #         "password": "yourpassword",  # Database password
            #         "database": "yourdatabase",  # Database name
            #     },
            # },
            # PostgreSQL configuration
            # Install with: tortoise-orm[asyncpg]
            # "postgres": {
            #     "engine": "tortoise.backends.asyncpg",
            #     "credentials": {
            #         "host": "localhost",  # Database host address
            #         "port": 5432,  # Database port
            #         "user": "yourusername",  # Database username
            #         "password": "yourpassword",  # Database password
            #         "database": "yourdatabase",  # Database name
            #     },
            # },
            # MSSQL/Oracle configuration
            # Install with: tortoise-orm[asyncodbc]
            # "oracle": {
            #     "engine": "tortoise.backends.asyncodbc",
            #     "credentials": {
            #         "host": "localhost",  # Database host address
            #         "port": 1433,  # Database port
            #         "user": "yourusername",  # Database username
            #         "password": "yourpassword",  # Database password
            #         "database": "yourdatabase",  # Database name
            #     },
            # },
            # SQLServer configuration
            # Install with: tortoise-orm[asyncodbc]
            # "sqlserver": {
            #     "engine": "tortoise.backends.asyncodbc",
            #     "credentials": {
            #         "host": "localhost",  # Database host address
            #         "port": 1433,  # Database port
            #         "user": "yourusername",  # Database username
            #         "password": "yourpassword",  # Database password
            #         "database": "yourdatabase",  # Database name
            #     },
            # },
        },
        "apps": {
            "models": {
                "models": ["app.models", "aerich.models"],
                "default_connection": "sqlite",
            },
        },
        "use_tz": False,  # Whether to use timezone-aware datetimes
        "timezone": "Asia/Shanghai",  # Timezone setting
    }
    DATETIME_FORMAT: str = "%Y-%m-%d %H:%M:%S"

    MINIO: dict = {
        # For local development with Python API running on host and MinIO in Docker:
        # Use "localhost:9000" when running Python API directly on the host
        # Use "minio:9000" when running Python API in Docker on the same network
        "host": os.environ.get("MINIO_HOST", "localhost:9000"),
        # These should match MINIO_ROOT_USER and MINIO_ROOT_PASSWORD in docker-compose.yaml
        "user": os.environ.get("MINIO_ROOT_USER", "minioadmin"),
        "password": os.environ.get("MINIO_ROOT_PASSWORD", "minioadmin"),
        "secure": os.environ.get("MINIO_SECURE", "False").lower() == "true",
        "bucket": os.environ.get("MINIO_BUCKET", "files")
    }

    # Default hash algorithm for file content hashing
    HASH_ALGORITHM: str = "sha256"

    # Redis Configuration
    REDIS_HOST: str = os.environ.get("REDIS_HOST", "localhost")
    REDIS_PORT: int = int(os.environ.get("REDIS_PORT", "6379"))
    REDIS_PASSWORD: str = os.environ.get("REDIS_PASSWORD", "redispassword")
    REDIS_DB: int = int(os.environ.get("REDIS_DB", "0"))
    REDIS_RESULTS_DB: int = int(os.environ.get("REDIS_RESULTS_DB", "1"))

    # Celery Configuration
    CELERY_BROKER_URL: str = os.environ.get(
        "CELERY_BROKER_URL",
        f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
    )
    CELERY_RESULT_BACKEND: str = os.environ.get(
        "CELERY_RESULT_BACKEND",
        f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_RESULTS_DB}"
    )

    # AI Configuration
    # 搜索引擎API密钥
    TAVILY_API_KEY: str = os.environ.get("TAVILY_API_KEY", "")
    BRAVE_SEARCH_API_KEY: str = os.environ.get("BRAVE_SEARCH_API_KEY", "")

    # LLM模型配置
    # Basic模型配置（用于一般任务）
    BASIC_MODEL: dict = {
        "base_url": os.environ.get("BASIC_MODEL_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
        "model": os.environ.get("BASIC_MODEL_NAME", "qwen-max-2025-01-25"),
        "api_key": os.environ.get("BASIC_MODEL_API_KEY", ""),
        "temperature": float(os.environ.get("BASIC_MODEL_TEMPERATURE", "0.7")),
        "max_tokens": int(os.environ.get("BASIC_MODEL_MAX_TOKENS", "2000"))
    }

    # Reasoning模型配置（用于复杂推理任务）
    REASONING_MODEL: dict = {
        "base_url": os.environ.get("REASONING_MODEL_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
        "model": os.environ.get("REASONING_MODEL_NAME", "qwen3-32b"),
        "api_key": os.environ.get("REASONING_MODEL_API_KEY", ""),
        "temperature": float(os.environ.get("REASONING_MODEL_TEMPERATURE", "0.1")),
        "max_tokens": int(os.environ.get("REASONING_MODEL_MAX_TOKENS", "4000"))
    }

    # Vision模型配置（用于视觉任务）
    VISION_MODEL: dict = {
        "base_url": os.environ.get("VISION_MODEL_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
        "model": os.environ.get("VISION_MODEL_NAME", "qwen2.5-vl-72b-instruct"),
        "api_key": os.environ.get("VISION_MODEL_API_KEY", ""),
        "temperature": float(os.environ.get("VISION_MODEL_TEMPERATURE", "0.1")),
        "max_tokens": int(os.environ.get("VISION_MODEL_MAX_TOKENS", "8000"))
    }

    # 搜索引擎配置
    DEFAULT_SEARCH_ENGINE: str = os.environ.get("DEFAULT_SEARCH_ENGINE", "duckduckgo")
    MAX_SEARCH_RESULTS: int = int(os.environ.get("MAX_SEARCH_RESULTS", "5"))

    # 爬虫配置
    CRAWL_TIMEOUT: int = int(os.environ.get("CRAWL_TIMEOUT", "30"))
    MAX_CRAWL_LENGTH: int = int(os.environ.get("MAX_CRAWL_LENGTH", "10000"))


settings = Settings()
